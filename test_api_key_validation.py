#!/usr/bin/env python3
"""
测试API key校验功能
"""

import subprocess
import json
import time

def test_api_key_validation():
    """测试API key校验功能"""
    
    print("🔐 测试API key校验功能")
    print("=" * 50)
    
    # 基础curl命令参数
    base_curl_params = [
        "curl", "-X", "POST", "http://127.0.0.1:8000/recognize",
        "-F", "outcar_image=@dvr_520.png",
        "-F", "mix_gaze=[0.37740352, -0.24775083, -0.89148018]",
        "-F", "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]",
        "-F", "gaze_valid=3",
        "-F", "prompt=这是什么车"
    ]
    
    # 测试1: 没有API key
    print(f"\n🧪 测试1: 没有API key")
    print("-" * 30)
    
    try:
        result = subprocess.run(base_curl_params, capture_output=True, text=True, timeout=30)
        print(f"状态码: {result.returncode}")
        if result.stdout:
            try:
                response = json.loads(result.stdout)
                print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试2: 错误的API key
    print(f"\n🧪 测试2: 错误的API key")
    print("-" * 30)
    
    wrong_key_params = base_curl_params + ["-H", "apl-key: wrong-api-key"]
    
    try:
        result = subprocess.run(wrong_key_params, capture_output=True, text=True, timeout=30)
        print(f"状态码: {result.returncode}")
        if result.stdout:
            try:
                response = json.loads(result.stdout)
                print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试3: 正确的API key
    print(f"\n🧪 测试3: 正确的API key")
    print("-" * 30)
    
    correct_key_params = base_curl_params + ["-H", "apl-key: sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"]
    
    try:
        result = subprocess.run(correct_key_params, capture_output=True, text=True, timeout=60)
        print(f"状态码: {result.returncode}")
        if result.stdout:
            try:
                response = json.loads(result.stdout)
                print(f"响应: {json.dumps(response, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试4: 自定义API key环境变量
    print(f"\n🧪 测试4: 自定义API key环境变量")
    print("-" * 30)
    print("注意: 这个测试需要重启服务才能生效")
    print("示例: API_KEY=custom-key python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000")

def show_usage_examples():
    """显示使用示例"""
    
    print(f"\n📖 API key使用示例")
    print("=" * 50)
    
    print(f"🔧 curl命令示例:")
    print(f"""
# 正确的API key
curl -X POST http://127.0.0.1:8000/recognize \\
  -H "apl-key: sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa" \\
  -F "outcar_image=@dvr_520.png" \\
  -F "mix_gaze=[0.37740352, -0.24775083, -0.89148018]" \\
  -F "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]" \\
  -F "gaze_valid=3" \\
  -F "prompt=这是什么车"

# 错误的API key (会返回401)
curl -X POST http://127.0.0.1:8000/recognize \\
  -H "apl-key: wrong-key" \\
  -F "outcar_image=@dvr_520.png" \\
  ...

# 没有API key (会返回401)
curl -X POST http://127.0.0.1:8000/recognize \\
  -F "outcar_image=@dvr_520.png" \\
  ...
""")
    
    print(f"🐍 Python requests示例:")
    print(f"""
import requests

# 正确的API key
headers = {
    'apl-key': 'sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa'
}

files = {
    'outcar_image': open('dvr_520.png', 'rb')
}

data = {
    'mix_gaze': '[0.37740352, -0.24775083, -0.89148018]',
    'gaze_origin': '[-29.56759495, -102.30740821, 520.42972422]',
    'gaze_valid': 3,
    'prompt': '这是什么车'
}

response = requests.post(
    'http://127.0.0.1:8000/recognize',
    headers=headers,
    files=files,
    data=data
)

print(response.json())
""")
    
    print(f"⚙️ 环境变量配置:")
    print(f"""
# 使用默认API key
python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000

# 使用自定义API key
API_KEY=your-custom-key python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000

# Docker环境
docker run -e API_KEY=your-custom-key your-image
""")
    
    print(f"📋 错误码说明:")
    print(f"""
- 1001: API key相关错误
  - Missing API key in header 'apl-key': 缺少API key
  - Invalid API key: API key无效
""")

def check_service_status():
    """检查服务状态"""
    
    print(f"🔍 检查服务状态")
    print("-" * 20)
    
    try:
        result = subprocess.run(
            ["curl", "-s", "http://127.0.0.1:8000/health"],
            capture_output=True, text=True, timeout=5
        )
        
        if result.returncode == 0:
            try:
                response = json.loads(result.stdout)
                print(f"✅ 服务运行正常: {response}")
                return True
            except:
                print(f"✅ 服务响应: {result.stdout}")
                return True
        else:
            print(f"❌ 服务无响应")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        return False

if __name__ == "__main__":
    # 显示使用示例
    show_usage_examples()
    
    # 检查服务状态
    if check_service_status():
        # 运行API key测试
        test_api_key_validation()
        
        print(f"\n📝 测试总结:")
        print(f"   - 测试1: 没有API key → 应该返回401错误")
        print(f"   - 测试2: 错误API key → 应该返回401错误")
        print(f"   - 测试3: 正确API key → 应该返回200成功")
        print(f"   - 默认API key: sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa")
    else:
        print(f"\n❌ 请先启动服务:")
        print(f"   python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000")
    
    print(f"\n✅ 测试完成!")
