#!/usr/bin/env python3
"""
测试坐标转换功能的脚本
"""

import cv2
import numpy as np
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from recognition_app.recognizer import convert_undist_to_original_coords, convert_undist_to_original_coords_search

def test_coordinate_conversion():
    """测试坐标转换功能"""
    
    # 读取相机配置
    camera_path = "data/vehicles/Pst/camera_info_20240212v4.json"
    try:
        with open(camera_path, 'r') as f:
            camera_config = json.load(f)
        print("✅ 成功读取相机配置")
    except Exception as e:
        print(f"❌ 读取相机配置失败: {e}")
        return
    
    # 获取相机参数
    outcar_config = camera_config['OutcarCamera']
    K = np.array(outcar_config['CameraIntrinsics'])
    dist = np.array(outcar_config.get('Distortion', [0.0, 0, 0, 0, 0]))
    cam_type = outcar_config.get('Camera_type', 'DMS')
    img_wh = outcar_config.get('ImgWH', (1920, 1080))
    
    print(f"📷 相机参数:")
    print(f"   类型: {cam_type}")
    print(f"   内参矩阵: {K.tolist()}")
    print(f"   畸变参数: {dist.tolist()}")
    print(f"   图像尺寸: {img_wh}")
    
    # 测试几个坐标点
    test_points_undist = [
        [960, 540],   # 图像中心
        [100, 100],   # 左上角
        [1820, 980],  # 右下角
        [500, 300],   # 随机点1
        [1400, 700],  # 随机点2
    ]
    
    print(f"\n🧪 开始测试坐标转换:")
    print(f"{'去畸变坐标':<15} -> {'原图坐标':<15} -> {'验证坐标':<15} -> {'误差':<10}")
    print("-" * 70)
    
    for undist_point in test_points_undist:
        undist_point = np.array(undist_point, dtype=np.float32)
        
        # 转换为原图坐标
        original_point = convert_undist_to_original_coords(undist_point, camera_config)
        
        # 验证：将原图坐标重新转换为去畸变坐标
        try:
            if cam_type in ['fisheye']:
                oriK = np.array(outcar_config['CameraIntrinsics_ori'], dtype=np.float64)
                dist_fisheye = np.array(dist[:4], dtype=np.float64)  # 鱼眼只需要4个畸变参数

                # 先将原图坐标转换为归一化坐标
                norm_point = cv2.fisheye.undistortPoints(
                    original_point.reshape(1, 1, 2), oriK, dist_fisheye
                )[0, 0]

                # 再投影到去畸变图像
                verify_undist, _ = cv2.projectPoints(
                    np.array([[[norm_point[0], norm_point[1], 1.0]]]),
                    np.zeros(3), np.zeros(3), K, np.zeros(5)
                )
                verify_undist = verify_undist[0, 0]
            else:
                # 使用标准畸变模型验证
                verify_undist = cv2.undistortPoints(
                    original_point.reshape(1, 1, 2), K, dist, None, K
                )[0, 0]
        except Exception as e:
            print(f"验证失败: {e}")
            verify_undist = undist_point
        
        # 计算误差
        error = np.linalg.norm(undist_point - verify_undist)
        
        print(f"{undist_point} -> {original_point} -> {verify_undist} -> {error:.2f}")

def create_test_image_with_grid():
    """创建一个带网格的测试图像来可视化畸变效果"""
    
    # 读取相机配置
    camera_path = "data/vehicles/Pst/camera_info_20240212v4.json"
    try:
        with open(camera_path, 'r') as f:
            camera_config = json.load(f)
    except Exception as e:
        print(f"❌ 读取相机配置失败: {e}")
        return
    
    outcar_config = camera_config['OutcarCamera']
    K = np.array(outcar_config['CameraIntrinsics'])
    dist = np.array(outcar_config.get('Distortion', [0.0, 0, 0, 0, 0]))
    cam_type = outcar_config.get('Camera_type', 'DMS')
    img_wh = outcar_config.get('ImgWH', (1920, 1080))
    
    # 创建网格图像
    img = np.zeros((img_wh[1], img_wh[0], 3), dtype=np.uint8)
    
    # 绘制网格
    grid_size = 100
    for x in range(0, img_wh[0], grid_size):
        cv2.line(img, (x, 0), (x, img_wh[1]), (100, 100, 100), 1)
    for y in range(0, img_wh[1], grid_size):
        cv2.line(img, (0, y), (img_wh[0], y), (100, 100, 100), 1)
    
    # 绘制中心十字
    center_x, center_y = img_wh[0] // 2, img_wh[1] // 2
    cv2.line(img, (center_x - 50, center_y), (center_x + 50, center_y), (0, 255, 0), 3)
    cv2.line(img, (center_x, center_y - 50), (center_x, center_y + 50), (0, 255, 0), 3)
    
    # 保存原图
    cv2.imwrite("test_grid_original.png", img)
    print("✅ 保存原图: test_grid_original.png")
    
    # 创建去畸变图像
    if cam_type in ['pinhole', 'DMS']:
        mapx, mapy = cv2.initUndistortRectifyMap(K, dist, np.zeros(3), K, img_wh, cv2.CV_32FC1)
        undist_img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
    elif cam_type in ['fisheye']:
        oriK = np.array(outcar_config['CameraIntrinsics_ori'])
        mapx, mapy = cv2.fisheye.initUndistortRectifyMap(oriK, dist, np.zeros(3), K, img_wh, cv2.CV_32FC1)
        undist_img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
    else:
        undist_img = img.copy()
    
    # 保存去畸变图像
    cv2.imwrite("test_grid_undistorted.png", undist_img)
    print("✅ 保存去畸变图像: test_grid_undistorted.png")
    
    # 测试坐标转换并在图像上标记
    test_points = [
        [960, 540],   # 中心
        [200, 200],   # 左上
        [1720, 880],  # 右下
        [500, 300],   # 测试点1
        [1400, 700],  # 测试点2
    ]
    
    result_img = img.copy()
    
    for i, undist_point in enumerate(test_points):
        undist_point = np.array(undist_point, dtype=np.float32)
        original_point = convert_undist_to_original_coords(undist_point, camera_config)
        
        # 在原图上标记转换后的点
        cv2.circle(result_img, tuple(original_point.astype(int)), 5, (0, 0, 255), -1)
        cv2.putText(result_img, f"P{i+1}", 
                   (int(original_point[0]) + 10, int(original_point[1]) - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 2)
        
        # 在去畸变图像上标记原始点
        cv2.circle(undist_img, tuple(undist_point.astype(int)), 5, (255, 0, 0), -1)
        cv2.putText(undist_img, f"P{i+1}", 
                   (int(undist_point[0]) + 10, int(undist_point[1]) - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0), 2)
    
    # 保存标记后的图像
    cv2.imwrite("test_grid_original_marked.png", result_img)
    cv2.imwrite("test_grid_undistorted_marked.png", undist_img)
    print("✅ 保存标记图像: test_grid_original_marked.png, test_grid_undistorted_marked.png")

if __name__ == "__main__":
    print("🔧 测试坐标转换功能")
    print("=" * 50)
    
    # 测试坐标转换
    test_coordinate_conversion()
    
    print("\n" + "=" * 50)
    print("🖼️ 创建可视化测试图像")
    
    # 创建可视化测试图像
    create_test_image_with_grid()
    
    print("\n✅ 测试完成!")
