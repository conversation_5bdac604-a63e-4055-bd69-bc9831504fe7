from fastapi import Fast<PERSON><PERSON>, UploadFile, Form, Header, HTTPException
from fastapi.responses import JSONResponse, StreamingResponse
import shutil, os, uuid
import json
from typing import List, Optional

from recognition_app.recognizer import init_model, recognize_gaze_async, recognize_gaze_streaming

app = FastAPI()
UPLOAD_FOLDER = "recognition_app/temp_images"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# API Key配置
DEFAULT_API_KEY = "sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"
VALID_API_KEY = os.getenv('VALID_API_KEY', DEFAULT_API_KEY)

def verify_api_key(api_key: Optional[str] = Header(None, alias="apl-key")):
    """验证API Key"""
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail={"code": 1001, "output": "Missing API key in header 'apl-key'"}
        )

    if api_key != VALID_API_KEY:
        raise HTTPException(
            status_code=401,
            detail={"code": 1001, "output": "Invalid API key"}
        )

    return api_key

@app.on_event("startup")
def startup_event():
    """服务启动时初始化模型"""
    init_model()  # 暂时注释掉模型初始化
    # print("服务启动完成（模型初始化已跳过）")

@app.get("/")
def read_root():
    """健康检查端点"""
    return {"message": "Recognition service is running", "status": "ok"}

@app.get("/health")
def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "recognition_app"}

@app.post("/recognize")
async def recognize_endpoint(
    outcar_image: UploadFile,
    mix_gaze: str = Form(..., description="gaze direction vector as JSON array, e.g. [0.37740352, -0.24775083, -0.89148018]"),
    gaze_origin: str = Form(..., description="gaze origin point as JSON array, e.g. [-29.56759495, -102.30740821, 520.42972422]"),
    gaze_valid: int = Form(..., description="gaze validity flag, e.g. 3"),
    prompt: str = Form("", description="prompt for vehicle recognition"),
    call_jarvis: bool = Form(False, description="whether to call JARVIS API for vehicle recognition"),
    stream: bool = Form(False, description="whether to use streaming mode for JARVIS API"),
    api_key: str = Header(..., alias="apl-key", description="API key for authentication")
):
    # 验证API Key
    if not api_key:
        return JSONResponse(
            content={"code": 1001, "output": "Missing API key in header 'apl-key'"},
            status_code=401
        )

    if api_key != VALID_API_KEY:
        return JSONResponse(
            content={"code": 1001, "output": "Invalid API key"},
            status_code=401
        )

    session_id = str(uuid.uuid4())
    outcar_path = os.path.join(UPLOAD_FOLDER, f"{session_id}_outcar.png")

    try:
        # 保存上传的outcar图片
        with open(outcar_path, "wb") as f:
            shutil.copyfileobj(outcar_image.file, f)

        # 解析gaze信息
        try:
            mix_gaze_array = json.loads(mix_gaze)
            gaze_origin_array = json.loads(gaze_origin)
        except json.JSONDecodeError as e:
            return JSONResponse(
                content={"code": 1006, "output": f"JSON解析错误: {e}"},
                status_code=400
            )

        # 验证gaze数据格式
        if not (isinstance(mix_gaze_array, list) and len(mix_gaze_array) == 3):
            return JSONResponse(
                content={"code": 1006, "output": "mix_gaze必须是包含3个元素的数组"},
                status_code=400
            )

        if not (isinstance(gaze_origin_array, list) and len(gaze_origin_array) == 3):
            return JSONResponse(
                content={"code": 1006, "output": "gaze_origin必须是包含3个元素的数组"},
                status_code=400
            )

        # 构建gaze输入数据
        gaze_input = {
            "mix_gaze": mix_gaze_array,
            "gaze_origin": gaze_origin_array,
            "gaze_valid": gaze_valid
        }

        # 暂时禁用流式功能，专注于修复基本功能
        # TODO: 重新实现流式功能
        if stream and call_jarvis and prompt and prompt.strip():
            # 流式功能暂时返回错误提示
            return JSONResponse(
                content={"code": 1999, "output": "流式功能正在维护中，请使用stream=false"},
                status_code=501
            )

        # 非流式返回
        result = await recognize_gaze_async(gaze_input, outcar_path, prompt, call_jarvis, stream)
        return JSONResponse(content=result)

    finally:
        # 清理临时文件
        if os.path.exists(outcar_path):
            os.remove(outcar_path)
