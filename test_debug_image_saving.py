#!/usr/bin/env python3
"""
测试DEBUG环境变量控制图片保存功能
"""

import os
import subprocess
import time
import glob

def test_debug_image_saving():
    """测试DEBUG环境变量控制图片保存功能"""
    
    print("🧪 测试DEBUG环境变量控制图片保存功能")
    print("=" * 50)
    
    # 清理之前的临时图片
    temp_dir = "recognition_app/temp_images"
    if os.path.exists(temp_dir):
        for file in glob.glob(f"{temp_dir}/*.png"):
            try:
                os.remove(file)
                print(f"清理旧文件: {file}")
            except:
                pass
    else:
        os.makedirs(temp_dir, exist_ok=True)
        print(f"创建临时目录: {temp_dir}")
    
    # 测试curl命令
    curl_cmd = [
        "curl", "-X", "POST", "http://127.0.0.1:8000/recognize",
        "-F", "outcar_image=@dvr_520.png",
        "-F", "mix_gaze=[0.37740352, -0.24775083, -0.89148018]",
        "-F", "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]",
        "-F", "gaze_valid=3",
        "-F", "prompt=这是什么车"
    ]
    
    # 测试1: DEBUG_SAVE_IMAGES=False (默认)
    print(f"\n🔍 测试1: DEBUG_SAVE_IMAGES=False (默认)")
    print("-" * 40)
    
    # 检查当前环境变量
    current_debug = os.getenv('DEBUG_SAVE_IMAGES', 'False')
    print(f"当前DEBUG_SAVE_IMAGES环境变量: {current_debug}")
    
    # 执行请求
    print("发送API请求...")
    try:
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ API请求成功")
            # 检查是否有图片文件生成
            image_files = glob.glob(f"{temp_dir}/*.png")
            if image_files:
                print(f"❌ 意外生成了图片文件: {image_files}")
            else:
                print("✅ 没有生成图片文件（符合预期）")
        else:
            print(f"❌ API请求失败: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("❌ API请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 等待一下
    time.sleep(2)
    
    # 测试2: DEBUG_SAVE_IMAGES=True
    print(f"\n🔍 测试2: DEBUG_SAVE_IMAGES=True")
    print("-" * 40)
    
    # 设置环境变量并重启服务
    print("设置DEBUG_SAVE_IMAGES=True并重启服务...")
    
    # 杀死现有服务
    try:
        subprocess.run(["pkill", "-f", "uvicorn.*recognition_app"], check=False)
        time.sleep(2)
    except:
        pass
    
    # 启动带DEBUG环境变量的服务
    env = os.environ.copy()
    env['DEBUG_SAVE_IMAGES'] = 'True'
    
    print("启动DEBUG模式的服务...")
    service_process = subprocess.Popen([
        "conda", "run", "-n", "outcabin_py3.9",
        "python", "-m", "uvicorn", "recognition_app.main:app",
        "--host", "0.0.0.0", "--port", "8000"
    ], env=env, cwd="/data/laiqinglong/outcabin/V2/outcabin")
    
    # 等待服务启动
    time.sleep(10)
    
    # 执行请求
    print("发送API请求...")
    try:
        result = subprocess.run(curl_cmd, capture_output=True, text=True, timeout=60)
        if result.returncode == 0:
            print("✅ API请求成功")
            # 检查是否有图片文件生成
            image_files = glob.glob(f"{temp_dir}/*.png")
            if image_files:
                print(f"✅ 成功生成图片文件: {image_files}")
                for file in image_files:
                    size = os.path.getsize(file)
                    print(f"   - {file}: {size} bytes")
            else:
                print("❌ 没有生成图片文件（不符合预期）")
        else:
            print(f"❌ API请求失败: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("❌ API请求超时")
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    # 清理
    try:
        service_process.terminate()
        service_process.wait(timeout=5)
    except:
        service_process.kill()
    
    print(f"\n📋 测试总结:")
    print(f"   - DEBUG_SAVE_IMAGES=False: 不保存图片")
    print(f"   - DEBUG_SAVE_IMAGES=True: 保存调试图片")
    print(f"   - 环境变量控制生效")

def show_debug_usage():
    """显示DEBUG环境变量的使用方法"""
    
    print(f"\n📖 DEBUG_SAVE_IMAGES 环境变量使用说明")
    print("=" * 50)
    
    print(f"🔧 设置方法:")
    print(f"   1. 临时设置 (当前会话):")
    print(f"      export DEBUG_SAVE_IMAGES=True")
    print(f"      python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000")
    print(f"")
    print(f"   2. 启动时设置:")
    print(f"      DEBUG_SAVE_IMAGES=True python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000")
    print(f"")
    print(f"   3. Docker环境:")
    print(f"      docker run -e DEBUG_SAVE_IMAGES=True ...")
    print(f"")
    print(f"🎯 支持的值:")
    print(f"   - True/true/1/yes/on: 启用图片保存")
    print(f"   - False/false/0/no/off/未设置: 禁用图片保存")
    print(f"")
    print(f"💾 保存的图片:")
    print(f"   - recognition_app/temp_images/gaze_point_<timestamp>.png: gaze落点图")
    print(f"   - recognition_app/temp_images/crop_img.png: 裁剪图片")
    print(f"   - recognition_app/temp_images/rect_img.png: 带检测框图片")
    print(f"")
    print(f"⚠️  注意事项:")
    print(f"   - 修改环境变量后需要重启服务")
    print(f"   - 确保temp_images目录有写入权限")
    print(f"   - 生产环境建议关闭DEBUG模式以节省存储空间")

if __name__ == "__main__":
    # 显示使用说明
    show_debug_usage()
    
    # 检查服务是否运行
    try:
        result = subprocess.run(["curl", "-s", "http://127.0.0.1:8000/"], 
                              capture_output=True, timeout=5)
        if result.returncode == 0:
            print(f"\n✅ 检测到服务正在运行，开始测试...")
            test_debug_image_saving()
        else:
            print(f"\n❌ 服务未运行，请先启动服务:")
            print(f"   python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000")
    except:
        print(f"\n❌ 无法连接到服务，请检查服务状态")
    
    print(f"\n✅ 测试完成!")
