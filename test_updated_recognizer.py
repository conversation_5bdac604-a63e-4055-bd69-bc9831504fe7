#!/usr/bin/env python3
"""
测试更新后的recognizer返回数据结构
"""

import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from recognition_app.recognizer import recognize_gaze_sync

def test_updated_recognizer():
    """测试更新后的recognizer返回数据结构"""
    
    print("🧪 测试更新后的recognizer返回数据结构")
    print("=" * 50)
    
    # 模拟gaze输入数据
    gaze_input = {
        "mix_gaze": [0.1, 0.2, 0.3],
        "gaze_origin": [0.0, 0.0, 0.0],
        "gaze_valid": True
    }
    
    # 使用一个测试图片路径（如果存在的话）
    test_image_paths = [
        "tt.jpg",
        "tt0.png", 
        "tt1.png",
        "tt2.png",
        "dms.png",
        "dvr.png"
    ]
    
    outcar_img_path = None
    for path in test_image_paths:
        if os.path.exists(path):
            outcar_img_path = path
            break
    
    if outcar_img_path is None:
        print("❌ 未找到测试图片，创建一个虚拟测试")
        # 模拟返回数据结构
        mock_result = {
            "code": 0,
            "output": "这是一辆白色的SUV车型",
            "itemID": "car1", 
            "2DRect_ori": [100, 200, 300, 400],
            "2DRects": [[100, 200, 300, 400], [150, 250, 350, 450]],
            "gaze_coordinates": {
                "type": "Cross",
                "point_2d_undist": [950, 538],
                "point_2d_original": [956.4, 538.8],
                "point_2d": [956.4, 538.8],
                "point_3d": [0.0, 0.0, 5.0],
                "color": [255, 255, 0]
            }
        }
        print("📋 模拟返回数据结构:")
        print(json.dumps(mock_result, indent=2, ensure_ascii=False))
        return
    
    print(f"📷 使用测试图片: {outcar_img_path}")
    
    try:
        # 调用更新后的函数
        result = recognize_gaze_sync(gaze_input, outcar_img_path, "这是什么车")
        
        print("📋 返回数据结构:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 验证返回数据结构
        print("\n🔍 数据结构验证:")
        
        required_fields = ["code", "output"]
        optional_fields = ["itemID", "2DRect_ori", "2DRects", "gaze_coordinates"]
        removed_fields = ["post_time"]
        
        # 检查必需字段
        for field in required_fields:
            if field in result:
                print(f"   ✅ {field}: {type(result[field])}")
            else:
                print(f"   ❌ 缺少必需字段: {field}")
        
        # 检查可选字段
        for field in optional_fields:
            if field in result:
                print(f"   ✅ {field}: {type(result[field])}")
            else:
                print(f"   ⚠️  可选字段未返回: {field}")
        
        # 检查已移除字段
        for field in removed_fields:
            if field in result:
                print(f"   ❌ 应该移除的字段仍存在: {field}")
            else:
                print(f"   ✅ 已移除字段: {field}")
        
        # 详细分析返回数据
        if result.get("code") == 0:
            print(f"\n🎯 成功返回数据分析:")
            print(f"   识别结果: {result.get('output', 'N/A')}")
            print(f"   车辆ID: {result.get('itemID', 'N/A')}")
            print(f"   原图坐标框: {result.get('2DRect_ori', 'N/A')}")
            print(f"   检测框数量: {len(result.get('2DRects', []))}")
            
            gaze_coords = result.get('gaze_coordinates')
            if gaze_coords:
                print(f"   Gaze坐标信息:")
                print(f"     类型: {gaze_coords.get('type', 'N/A')}")
                print(f"     去畸变坐标: {gaze_coords.get('point_2d_undist', 'N/A')}")
                print(f"     原图坐标: {gaze_coords.get('point_2d_original', 'N/A')}")
                print(f"     3D坐标: {gaze_coords.get('point_3d', 'N/A')}")
        else:
            print(f"\n❌ 错误返回:")
            print(f"   错误码: {result.get('code')}")
            print(f"   输出: {result.get('output')}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_error_cases():
    """测试错误情况的返回数据结构"""
    
    print(f"\n🚨 测试错误情况的返回数据结构")
    print("-" * 40)
    
    # 测试图片加载失败
    gaze_input = {
        "mix_gaze": [0.1, 0.2, 0.3],
        "gaze_origin": [0.0, 0.0, 0.0], 
        "gaze_valid": True
    }
    
    try:
        result = recognize_gaze_sync(gaze_input, "non_existent_image.jpg", "这是什么车")
        print("📋 图片加载失败的返回数据:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 验证错误返回不包含post_time
        if "post_time" in result:
            print("   ❌ 错误返回中仍包含post_time")
        else:
            print("   ✅ 错误返回中已移除post_time")
            
    except Exception as e:
        print(f"❌ 错误测试失败: {e}")

def show_expected_structure():
    """显示期望的返回数据结构"""
    
    print(f"\n📋 期望的返回数据结构:")
    print("-" * 30)
    
    success_structure = {
        "code": 0,
        "output": "识别结果文本",
        "itemID": "car1",
        "2DRect_ori": [100, 200, 300, 400],
        "2DRects": [[100, 200, 300, 400], [150, 250, 350, 450]],
        "gaze_coordinates": {
            "type": "Cross",
            "point_2d_undist": [950, 538],
            "point_2d_original": [956.4, 538.8],
            "point_2d": [956.4, 538.8],
            "point_3d": [0.0, 0.0, 5.0],
            "color": [255, 255, 0]
        }
    }
    
    error_structure = {
        "code": 1002,  # 错误码
        "output": None
    }
    
    print("✅ 成功返回结构:")
    print(json.dumps(success_structure, indent=2, ensure_ascii=False))
    
    print("\n❌ 错误返回结构:")
    print(json.dumps(error_structure, indent=2, ensure_ascii=False))
    
    print("\n📝 字段说明:")
    print("   - code: 错误码 (0=成功)")
    print("   - output: 识别结果或None")
    print("   - itemID: 车辆标识符")
    print("   - 2DRect_ori: 原图坐标系中的检测框")
    print("   - 2DRects: 所有检测框列表")
    print("   - gaze_coordinates: gaze落点坐标信息")
    print("   - 已移除: post_time 字段")

if __name__ == "__main__":
    # 显示期望结构
    show_expected_structure()
    
    # 测试正常情况
    test_updated_recognizer()
    
    # 测试错误情况
    test_error_cases()
    
    print(f"\n✅ 测试完成!")
