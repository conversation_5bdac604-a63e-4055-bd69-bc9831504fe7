# OutCabin Vehicle Recognition API

基于 gaze 检测和 JARVIS AI 的车辆识别 API 服务，支持通过视线追踪定位车辆并进行智能识别。

## 功能特性

- 🚗 **车辆检测**: 基于 gaze 信息精确定位图像中的车辆
- 🤖 **AI 识别**: 集成 JARVIS API 进行车辆型号、品牌等智能识别
- 📍 **坐标计算**: 提供精确的 2D 坐标和检测框信息
- 🆔 **请求追踪**: 返回 JARVIS API 的唯一 ID 用于请求追踪
- ⚡ **灵活控制**: 支持选择性调用 AI 识别功能
- 🔒 **安全认证**: API 密钥认证机制

## 快速开始

### 环境要求

- Python 3.9+
- OpenCV
- FastAPI
- Uvicorn

### 安装依赖

```bash
conda activate outcabin_py3.9
pip install fastapi uvicorn opencv-python requests
```

### 启动服务

```bash
python -m uvicorn recognition_app.main:app --host 0.0.0.0 --port 8000
```

服务启动后访问: http://localhost:8000

## API 接口

### POST /recognize

车辆识别接口，支持基于 gaze 信息的车辆检测和 AI 识别。

#### 请求参数

| 参数           | 类型    | 必需 | 默认值  | 描述                           |
| -------------- | ------- | ---- | ------- | ------------------------------ |
| `outcar_image` | File    | ✅   | -       | 车辆图片文件                   |
| `mix_gaze`     | String  | ✅   | -       | gaze 方向向量，JSON 数组格式   |
| `gaze_origin`  | String  | ✅   | -       | gaze 起点坐标，JSON 数组格式   |
| `gaze_valid`   | Integer | ✅   | -       | gaze 有效性标志                |
| `prompt`       | String  | ❌   | `""`    | AI 识别提示词                  |
| `call_jarvis`  | Boolean | ❌   | `false` | 是否调用 JARVIS AI 识别        |
| `stream`       | Boolean | ❌   | `false` | 是否使用流式返回（当前维护中） |

#### 请求头

| 参数      | 类型   | 必需 | 描述         |
| --------- | ------ | ---- | ------------ |
| `apl-key` | String | ✅   | API 认证密钥 |

#### 响应格式

```json
{
  "code": 0,
  "output": "识别结果或状态信息",
  "itemID": "检测到的车辆ID",
  "2DRect_ori": [x1, y1, x2, y2],
  "2DRects": [[x1, y1, x2, y2], ...],
  "gaze_point": [x, y],
  "id": "jarvis_api_id_or_null"
}
```

#### 响应字段说明

| 字段         | 类型         | 描述                                      |
| ------------ | ------------ | ----------------------------------------- |
| `code`       | Integer      | 状态码，0 表示成功                        |
| `output`     | String       | 识别结果或状态信息                        |
| `itemID`     | String       | 检测到的车辆项目 ID                       |
| `2DRect_ori` | Array        | 原始检测框坐标 [x1, y1, x2, y2]           |
| `2DRects`    | Array        | 所有检测框列表                            |
| `gaze_point` | Array        | gaze 点在图像中的坐标 [x, y]              |
| `id`         | String\|null | JARVIS API 返回的唯一 ID，未调用时为 null |

## 使用示例

### cURL 示例

#### 基础车辆检测（不调用 AI）

```bash
curl -X POST http://localhost:8000/recognize \
  -H "apl-key: your-api-key" \
  -F "outcar_image=@car.jpg" \
  -F "mix_gaze=[0.37740352, -0.24775083, -0.89148018]" \
  -F "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]" \
  -F "gaze_valid=3"
```

#### 完整 AI 识别

```bash
curl -X POST http://localhost:8000/recognize \
  -H "apl-key: your-api-key" \
  -F "outcar_image=@car.jpg" \
  -F "mix_gaze=[0.37740352, -0.24775083, -0.89148018]" \
  -F "gaze_origin=[-29.56759495, -102.30740821, 520.42972422]" \
  -F "gaze_valid=3" \
  -F "call_jarvis=true" \
  -F "prompt=这是什么车"
```

### Python 示例

```python
import requests

# API配置
url = "http://localhost:8000/recognize"
headers = {"apl-key": "your-api-key"}

# 准备数据
files = {"outcar_image": open("car.jpg", "rb")}
data = {
    "mix_gaze": "[0.37740352, -0.24775083, -0.89148018]",
    "gaze_origin": "[-29.56759495, -102.30740821, 520.42972422]",
    "gaze_valid": 3,
    "call_jarvis": True,
    "prompt": "这是什么车"
}

# 发送请求
response = requests.post(url, headers=headers, files=files, data=data)
result = response.json()

print(f"识别结果: {result['output']}")
print(f"检测框: {result['2DRect_ori']}")
print(f"gaze点: {result['gaze_point']}")
print(f"JARVIS ID: {result['id']}")
```

### JavaScript 示例

```javascript
const formData = new FormData();
formData.append("outcar_image", fileInput.files[0]);
formData.append("mix_gaze", "[0.37740352, -0.24775083, -0.89148018]");
formData.append("gaze_origin", "[-29.56759495, -102.30740821, 520.42972422]");
formData.append("gaze_valid", "3");
formData.append("call_jarvis", "true");
formData.append("prompt", "这是什么车");

fetch("http://localhost:8000/recognize", {
  method: "POST",
  headers: {
    "apl-key": "your-api-key",
  },
  body: formData,
})
  .then((response) => response.json())
  .then((data) => {
    console.log("识别结果:", data.output);
    console.log("JARVIS ID:", data.id);
  });
```

## 功能说明

### Gaze 检测

系统使用 gaze 信息（视线追踪数据）来定位图像中的目标车辆：

- `mix_gaze`: 视线方向向量，3D 坐标
- `gaze_origin`: 视线起点，3D 坐标
- `gaze_valid`: 视线数据有效性标志

### AI 识别控制

通过参数控制是否调用 JARVIS AI 进行车辆识别：

- `call_jarvis=false`: 仅进行车辆检测，返回坐标信息
- `call_jarvis=true` + `prompt`: 调用 AI 识别，返回详细车辆信息

### 调用条件

JARVIS AI 只有在以下条件同时满足时才会被调用：

1. `call_jarvis = true`
2. `prompt` 不为空且不为纯空格

## 错误码

| 错误码 | 说明                |
| ------ | ------------------- |
| 0      | 成功                |
| 1001   | 图片读取失败        |
| 1002   | 图片加载失败        |
| 1003   | 图片裁剪失败        |
| 1004   | JARVIS API 请求失败 |
| 1005   | 响应解析失败        |
| 1006   | 格式错误            |
| 1007   | 未检测到 gaze       |
| 1999   | 流式功能维护中      |

## 测试工具

项目提供了完整的测试工具：

### Python 测试脚本

```bash
python test_gaze_api.py
```

### Bash 测试脚本

```bash
chmod +x test_gaze_curl.sh
./test_gaze_curl.sh
```

## 部署说明

### 生产环境

```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker recognition_app.main:app --bind 0.0.0.0:8000

# 或使用Docker
docker build -t outcabin-api .
docker run -p 8000:8000 outcabin-api
```

### 环境变量

```bash
export API_KEY="your-jarvis-api-key"
export JARVIS_API_URL="https://jarvis.senseauto.com:1050/v1/2024_byd_poc"
```

## 性能优化

- 使用异步处理提高并发性能
- 支持图片格式自动转换
- 智能错误处理和重试机制
- 内存优化的图片处理

## 安全考虑

- API 密钥认证
- 文件类型验证
- 请求大小限制
- 错误信息脱敏

## 响应示例

### 成功响应（调用 AI）

```json
{
  "code": 0,
  "output": "这是一辆荣威Ei5。",
  "itemID": "car1",
  "2DRect_ori": [236, 457, 552, 653],
  "2DRects": [
    [236, 457, 552, 653],
    [476, 492, 668, 604]
  ],
  "gaze_point": [305, 460],
  "id": "cd47a9c5-a077-44ac-a7ba-1b7e84958055"
}
```

### 成功响应（未调用 AI）

```json
{
  "code": 0,
  "output": "未启用车辆识别",
  "itemID": "car1",
  "2DRect_ori": [236, 457, 552, 653],
  "2DRects": [
    [236, 457, 552, 653],
    [476, 492, 668, 604]
  ],
  "gaze_point": [305, 460],
  "id": null
}
```

### 错误响应

```json
{
  "code": 1001,
  "output": "图片读取失败"
}
```

## 更新日志

### v2.0.0

- ✅ 添加 JARVIS AI 识别功能
- ✅ 支持选择性 AI 调用
- ✅ 返回 JARVIS API 唯一 ID
- ✅ 优化错误处理机制
- ⚠️ 流式返回功能（维护中）

### v1.0.0

- ✅ 基础 gaze 检测功能
- ✅ 车辆坐标定位
- ✅ RESTful API 接口

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
