#!/bin/bash

# 测试新的gaze接口的curl命令 - 支持call_jarvis参数控制

echo "🤖 测试gaze接口 - call_jarvis参数控制功能"
echo "=================================================="

# 基础参数
BASE_URL="http://127.0.0.1:8000/recognize"
API_KEY="sk-5qR2w8yG7FhJ9K1xL0MbNvEtZ3SuPdQa"
IMAGE_FILE="dvr_520.png"
GAZE_DATA="-F mix_gaze=[0.37740352,-0.24775083,-0.89148018] -F gaze_origin=[-29.56759495,-102.30740821,520.42972422] -F gaze_valid=3"

echo ""
echo "🧪 场景1: 默认参数 (call_jarvis=false, prompt=\"\")"
echo "--------------------------------------------------"
echo "预期: 不调用JARVIS API，返回'未启用车辆识别'"
echo ""

curl -X POST $BASE_URL \
  -H "apl-key: $API_KEY" \
  -F "outcar_image=@$IMAGE_FILE" \
  $GAZE_DATA

echo -e "\n"
echo "🧪 场景2: call_jarvis=false, prompt不为空"
echo "--------------------------------------------------"
echo "预期: 不调用JARVIS API，返回'未启用车辆识别'"
echo ""

curl -X POST $BASE_URL \
  -H "apl-key: $API_KEY" \
  -F "outcar_image=@$IMAGE_FILE" \
  $GAZE_DATA \
  -F "call_jarvis=false" \
  -F "prompt=这是什么车"

echo -e "\n"
echo "🧪 场景3: call_jarvis=true, prompt为空"
echo "--------------------------------------------------"
echo "预期: 不调用JARVIS API，返回'prompt为空，跳过车辆识别'"
echo ""

curl -X POST $BASE_URL \
  -H "apl-key: $API_KEY" \
  -F "outcar_image=@$IMAGE_FILE" \
  $GAZE_DATA \
  -F "call_jarvis=true"

echo -e "\n"
echo "🧪 场景4: call_jarvis=true, prompt不为空"
echo "--------------------------------------------------"
echo "预期: 调用JARVIS API，返回实际识别结果"
echo ""

curl -X POST $BASE_URL \
  -H "apl-key: $API_KEY" \
  -F "outcar_image=@$IMAGE_FILE" \
  $GAZE_DATA \
  -F "call_jarvis=true" \
  -F "prompt=这是什么车"

echo -e "\n"
echo "📝 测试总结:"
echo "   - 场景1: 默认行为，不调用API (id=null)"
echo "   - 场景2: 显式禁用API调用 (id=null)"
echo "   - 场景3: 启用API但prompt为空 (id=null)"
echo "   - 场景4: 完整调用API (id=JARVIS返回的ID)"
echo "   - 调用条件: call_jarvis=true AND prompt不为空"
echo "   - ID字段: 来自JARVIS API响应，未调用时为null"
echo ""
echo "✅ 所有测试场景完成"
